from rest_framework import serializers
from django.contrib.auth import get_user_model
from django.core.validators import validate_email
from .models import SystemSetting, EmailTemplate, BackupConfig
import json

User = get_user_model()


class SystemSettingSerializer(serializers.ModelSerializer):
    """系统设置序列化器"""
    updated_by_name = serializers.CharField(source='updated_by.get_full_name', read_only=True)
    
    class Meta:
        model = SystemSetting
        fields = [
            'id', 'key', 'value', 'description',
            'category', 'data_type', 'is_public',
            'updated_by', 'updated_by_name', 'updated_at'
        ]
        read_only_fields = ['id', 'updated_by', 'updated_at']
    
    def validate_value(self, value):
        """验证设置值"""
        key = self.initial_data.get('key') or (self.instance.key if self.instance else None)
        
        # 根据不同的设置项进行特定验证
        if key == 'password_min_length':
            try:
                length = int(value)
                if length < 6 or length > 128:
                    raise serializers.ValidationError("密码最小长度必须在6-128之间")
            except ValueError:
                raise serializers.ValidationError("密码最小长度必须是数字")
        
        elif key == 'password_max_age_days':
            try:
                days = int(value)
                if days < 0 or days > 365:
                    raise serializers.ValidationError("密码有效期必须在0-365天之间")
            except ValueError:
                raise serializers.ValidationError("密码有效期必须是数字")
        
        elif key == 'session_timeout_minutes':
            try:
                minutes = int(value)
                if minutes < 5 or minutes > 1440:  # 5分钟到24小时
                    raise serializers.ValidationError("会话超时时间必须在5-1440分钟之间")
            except ValueError:
                raise serializers.ValidationError("会话超时时间必须是数字")
        
        elif key == 'max_login_attempts':
            try:
                attempts = int(value)
                if attempts < 3 or attempts > 10:
                    raise serializers.ValidationError("最大登录尝试次数必须在3-10之间")
            except ValueError:
                raise serializers.ValidationError("最大登录尝试次数必须是数字")
        
        elif key == 'account_lockout_duration_minutes':
            try:
                minutes = int(value)
                if minutes < 5 or minutes > 1440:
                    raise serializers.ValidationError("账户锁定时间必须在5-1440分钟之间")
            except ValueError:
                raise serializers.ValidationError("账户锁定时间必须是数字")
        
        elif key in ['smtp_host', 'smtp_username']:
            if not value.strip():
                raise serializers.ValidationError("此设置项不能为空")
        
        elif key == 'smtp_port':
            try:
                port = int(value)
                if port < 1 or port > 65535:
                    raise serializers.ValidationError("SMTP端口必须在1-65535之间")
            except ValueError:
                raise serializers.ValidationError("SMTP端口必须是数字")
        
        elif key == 'admin_email':
            if value:
                validate_email(value)
        
        elif key in ['enable_mfa', 'enable_email_notifications', 'enable_audit_log', 
                     'enable_password_history', 'smtp_use_tls', 'smtp_use_ssl']:
            if value.lower() not in ['true', 'false', '1', '0', 'yes', 'no']:
                raise serializers.ValidationError("此设置项必须是布尔值")
        
        return value


class SystemSettingUpdateSerializer(serializers.Serializer):
    """系统设置批量更新序列化器"""
    settings = serializers.DictField(
        child=serializers.CharField(),
        help_text="设置项键值对"
    )
    
    def validate_settings(self, value):
        """验证设置项"""
        if not value:
            raise serializers.ValidationError("设置项不能为空")
        
        # 检查设置项是否存在
        existing_keys = set(SystemSetting.objects.values_list('key', flat=True))
        invalid_keys = set(value.keys()) - existing_keys
        
        if invalid_keys:
            raise serializers.ValidationError(
                f"以下设置项不存在: {', '.join(invalid_keys)}"
            )
        
        return value
    
    def update_settings(self, validated_data, user):
        """批量更新设置"""
        settings = validated_data['settings']
        updated_settings = []
        
        for key, value in settings.items():
            try:
                setting = SystemSetting.objects.get(key=key)
                
                # 使用单个设置的序列化器进行验证
                serializer = SystemSettingSerializer(
                    setting,
                    data={'value': value},
                    partial=True
                )
                
                if serializer.is_valid():
                    setting = serializer.save(updated_by=user)
                    updated_settings.append(setting)
                else:
                    raise serializers.ValidationError({
                        key: serializer.errors.get('value', ['验证失败'])
                    })
            
            except SystemSetting.DoesNotExist:
                raise serializers.ValidationError({
                    key: ['设置项不存在']
                })
        
        return updated_settings


class EmailTemplateSerializer(serializers.ModelSerializer):
    """邮件模板序列化器"""
    updated_by_name = serializers.CharField(source='updated_by.get_full_name', read_only=True)
    
    class Meta:
        model = EmailTemplate
        fields = [
            'id', 'name', 'subject', 'content',
            'template_type', 'is_active',
            'updated_by', 'updated_by_name',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'updated_by', 'created_at', 'updated_at']
    
    def validate_content(self, value):
        """验证邮件内容"""
        if not value.strip():
            raise serializers.ValidationError("邮件内容不能为空")
        
        # 检查必要的模板变量
        template_type = self.initial_data.get('template_type') or (
            self.instance.template_type if self.instance else None
        )
        
        required_vars = {
            'password_reset': ['{{ user_name }}', '{{ reset_link }}'],
            'password_shared': ['{{ shared_by }}', '{{ password_title }}'],
            'account_locked': ['{{ user_name }}', '{{ lockout_time }}'],
            'security_alert': ['{{ user_name }}', '{{ event_description }}'],
        }
        
        if template_type in required_vars:
            missing_vars = []
            for var in required_vars[template_type]:
                if var not in value:
                    missing_vars.append(var)
            
            if missing_vars:
                raise serializers.ValidationError(
                    f"邮件模板缺少必要变量: {', '.join(missing_vars)}"
                )
        
        return value


class BackupConfigSerializer(serializers.ModelSerializer):
    """备份配置序列化器"""
    updated_by_name = serializers.CharField(source='updated_by.get_full_name', read_only=True)
    next_backup_time = serializers.SerializerMethodField()
    
    class Meta:
        model = BackupConfig
        fields = [
            'id', 'name', 'backup_type', 'schedule_type',
            'schedule_time', 'retention_days', 'storage_path',
            'is_enabled', 'last_backup_time', 'next_backup_time',
            'backup_size', 'updated_by', 'updated_by_name',
            'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'last_backup_time', 'backup_size',
            'updated_by', 'created_at', 'updated_at'
        ]
    
    def get_next_backup_time(self, obj):
        """获取下次备份时间"""
        return obj.get_next_backup_time()
    
    def validate_retention_days(self, value):
        """验证保留天数"""
        if value < 1 or value > 365:
            raise serializers.ValidationError("保留天数必须在1-365之间")
        return value
    
    def validate_schedule_time(self, value):
        """验证调度时间"""
        schedule_type = self.initial_data.get('schedule_type') or (
            self.instance.schedule_type if self.instance else None
        )
        
        if schedule_type == 'daily':
            # 验证时间格式 HH:MM
            try:
                hour, minute = value.split(':')
                hour, minute = int(hour), int(minute)
                if not (0 <= hour <= 23 and 0 <= minute <= 59):
                    raise ValueError
            except (ValueError, AttributeError):
                raise serializers.ValidationError("每日备份时间格式应为 HH:MM")
        
        elif schedule_type == 'weekly':
            # 验证格式 weekday:HH:MM (0-6:HH:MM)
            try:
                weekday, time_part = value.split(':')
                weekday = int(weekday)
                hour, minute = time_part.split(':')
                hour, minute = int(hour), int(minute)
                if not (0 <= weekday <= 6 and 0 <= hour <= 23 and 0 <= minute <= 59):
                    raise ValueError
            except (ValueError, AttributeError):
                raise serializers.ValidationError("每周备份时间格式应为 weekday:HH:MM (0-6:HH:MM)")
        
        elif schedule_type == 'monthly':
            # 验证格式 day:HH:MM (1-31:HH:MM)
            try:
                day, time_part = value.split(':')
                day = int(day)
                hour, minute = time_part.split(':')
                hour, minute = int(hour), int(minute)
                if not (1 <= day <= 31 and 0 <= hour <= 23 and 0 <= minute <= 59):
                    raise ValueError
            except (ValueError, AttributeError):
                raise serializers.ValidationError("每月备份时间格式应为 day:HH:MM (1-31:HH:MM)")
        
        return value


class SystemStatusSerializer(serializers.Serializer):
    """系统状态序列化器"""
    database_status = serializers.CharField(read_only=True)
    redis_status = serializers.CharField(read_only=True)
    email_status = serializers.CharField(read_only=True)
    backup_status = serializers.CharField(read_only=True)
    
    # 系统信息
    system_version = serializers.CharField(read_only=True)
    python_version = serializers.CharField(read_only=True)
    django_version = serializers.CharField(read_only=True)
    
    # 统计信息
    total_users = serializers.IntegerField(read_only=True)
    total_passwords = serializers.IntegerField(read_only=True)
    total_shares = serializers.IntegerField(read_only=True)
    
    # 存储信息
    disk_usage = serializers.DictField(read_only=True)
    database_size = serializers.CharField(read_only=True)
    
    # 最近活动
    recent_logins = serializers.IntegerField(read_only=True)
    recent_operations = serializers.IntegerField(read_only=True)
    
    # 安全状态
    security_alerts = serializers.IntegerField(read_only=True)
    failed_login_attempts = serializers.IntegerField(read_only=True)


class SystemMaintenanceSerializer(serializers.Serializer):
    """系统维护序列化器"""
    action = serializers.ChoiceField(
        choices=[
            ('clear_logs', '清理日志'),
            ('optimize_database', '优化数据库'),
            ('clear_cache', '清理缓存'),
            ('backup_now', '立即备份'),
            ('test_email', '测试邮件'),
        ],
        help_text="维护操作类型"
    )
    
    parameters = serializers.DictField(
        required=False,
        help_text="操作参数"
    )
    
    def validate(self, attrs):
        """验证维护操作参数"""
        action = attrs['action']
        parameters = attrs.get('parameters', {})
        
        if action == 'clear_logs':
            # 验证日志清理参数
            days = parameters.get('days')
            if days is not None:
                try:
                    days = int(days)
                    if days < 1 or days > 365:
                        raise serializers.ValidationError({
                            'parameters': {'days': '保留天数必须在1-365之间'}
                        })
                except ValueError:
                    raise serializers.ValidationError({
                        'parameters': {'days': '保留天数必须是数字'}
                    })
        
        elif action == 'test_email':
            # 验证测试邮件参数
            email = parameters.get('email')
            if not email:
                raise serializers.ValidationError({
                    'parameters': {'email': '测试邮件地址不能为空'}
                })
            try:
                validate_email(email)
            except Exception:
                raise serializers.ValidationError({
                    'parameters': {'email': '邮件地址格式不正确'}
                })
        
        return attrs