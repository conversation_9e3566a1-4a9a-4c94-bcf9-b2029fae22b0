from rest_framework import generics, status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.views import APIView
from django.shortcuts import get_object_or_404
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.db import transaction
from django.core.mail import send_mail
from django.conf import settings
from django.template.loader import render_to_string
from .models import SharedPassword, ShareLink
from .serializers import (
    SharedPasswordSerializer,
    SharedPasswordCreateSerializer,
    ShareLinkSerializer,
    ShareLinkAccessSerializer,
    SharedPasswordDetailSerializer,
    ShareLinkPasswordSerializer
)
from apps.passwords.models import PasswordEntry
from apps.audit.models import OperationLog, AccessLog
from utils.permissions import IsOwnerOrSharedWith
import logging

User = get_user_model()
logger = logging.getLogger(__name__)


class SharedPasswordListCreateView(generics.ListCreateAPIView):
    """共享密码列表和创建视图"""
    permission_classes = [permissions.IsAuthenticated]
    
    def get_serializer_class(self):
        if self.request.method == 'POST':
            return SharedPasswordCreateSerializer
        return SharedPasswordSerializer
    
    def get_queryset(self):
        """获取当前用户的共享密码"""
        user = self.request.user
        return SharedPassword.objects.filter(
            shared_by=user,
            is_active=True
        ).select_related(
            'password_entry', 'shared_by', 'shared_with'
        ).order_by('-created_at')
    
    def perform_create(self, serializer):
        """创建共享密码"""
        with transaction.atomic():
            shared_passwords = serializer.save(shared_by=self.request.user)
            
            # 记录操作日志
            for shared_password in shared_passwords:
                OperationLog.objects.create(
                    user=self.request.user,
                    action='share_password',
                    resource_type='password_entry',
                    resource_id=shared_password.password_entry.id,
                    details={
                        'shared_with': shared_password.shared_with.email,
                        'permission_level': shared_password.permission_level,
                        'expires_at': shared_password.expires_at.isoformat() if shared_password.expires_at else None
                    }
                )
                
                # 发送通知邮件
                self.send_share_notification(shared_password)
    
    def send_share_notification(self, shared_password):
        """发送分享通知邮件"""
        try:
            subject = f'{shared_password.shared_by.get_full_name()} 与您分享了密码'
            context = {
                'shared_by': shared_password.shared_by,
                'shared_with': shared_password.shared_with,
                'password_entry': shared_password.password_entry,
                'permission_level': shared_password.get_permission_level_display(),
                'expires_at': shared_password.expires_at,
            }
            
            html_message = render_to_string('emails/password_shared.html', context)
            
            send_mail(
                subject=subject,
                message='',
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[shared_password.shared_with.email],
                html_message=html_message,
                fail_silently=True
            )
        except Exception as e:
            logger.error(f"发送分享通知邮件失败: {e}")


class SharedPasswordDetailView(generics.RetrieveUpdateDestroyAPIView):
    """共享密码详情视图"""
    serializer_class = SharedPasswordSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        return SharedPassword.objects.filter(
            shared_by=self.request.user
        ).select_related('password_entry', 'shared_by', 'shared_with')
    
    def perform_update(self, serializer):
        """更新共享密码"""
        instance = serializer.save()
        
        # 记录操作日志
        OperationLog.objects.create(
            user=self.request.user,
            action='update_shared_password',
            resource_type='shared_password',
            resource_id=instance.id,
            details={
                'shared_with': instance.shared_with.email,
                'permission_level': instance.permission_level,
                'expires_at': instance.expires_at.isoformat() if instance.expires_at else None
            }
        )
    
    def perform_destroy(self, instance):
        """删除共享密码（软删除）"""
        instance.is_active = False
        instance.save()
        
        # 记录操作日志
        OperationLog.objects.create(
            user=self.request.user,
            action='revoke_shared_password',
            resource_type='shared_password',
            resource_id=instance.id,
            details={
                'shared_with': instance.shared_with.email,
                'password_entry_title': instance.password_entry.title
            }
        )


class ReceivedSharedPasswordListView(generics.ListAPIView):
    """接收到的共享密码列表视图"""
    serializer_class = SharedPasswordSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """获取当前用户接收到的共享密码"""
        return SharedPassword.objects.filter(
            shared_with=self.request.user,
            is_active=True
        ).select_related(
            'password_entry', 'shared_by', 'shared_with'
        ).order_by('-created_at')


class SharedPasswordAccessView(generics.RetrieveAPIView):
    """共享密码访问视图"""
    serializer_class = SharedPasswordDetailSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        return SharedPassword.objects.filter(
            shared_with=self.request.user,
            is_active=True
        ).select_related('password_entry', 'shared_by')
    
    def retrieve(self, request, *args, **kwargs):
        """访问共享密码"""
        instance = self.get_object()
        
        # 检查是否过期
        if instance.expires_at and instance.expires_at <= timezone.now():
            return Response(
                {'error': '共享密码已过期'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        # 记录访问日志
        AccessLog.objects.create(
            user=request.user,
            resource_type='shared_password',
            resource_id=instance.id,
            action='access_shared_password',
            ip_address=request.META.get('REMOTE_ADDR'),
            user_agent=request.META.get('HTTP_USER_AGENT', ''),
            details={
                'shared_by': instance.shared_by.email,
                'password_entry_title': instance.password_entry.title
            }
        )
        
        serializer = self.get_serializer(instance)
        return Response(serializer.data)


class ShareLinkListCreateView(generics.ListCreateAPIView):
    """分享链接列表和创建视图"""
    serializer_class = ShareLinkSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """获取当前用户的分享链接"""
        return ShareLink.objects.filter(
            created_by=self.request.user,
            is_active=True
        ).select_related('password_entry').order_by('-created_at')
    
    def perform_create(self, serializer):
        """创建分享链接"""
        instance = serializer.save(created_by=self.request.user)
        
        # 记录操作日志
        OperationLog.objects.create(
            user=self.request.user,
            action_type='password_share',
            target_type='password_entry',
            target_id=str(instance.password_entry.id),
            description=f'创建分享链接: {instance.password_entry.title}',
            ip_address=self.request.META.get('REMOTE_ADDR', ''),
            user_agent=self.request.META.get('HTTP_USER_AGENT', ''),
            extra_data={
                'token': instance.token,
                'expires_at': instance.expires_at.isoformat() if instance.expires_at else None,
                'max_access_count': instance.max_access_count,
                'require_password': instance.require_password
            }
        )


class ShareLinkDetailView(generics.RetrieveUpdateDestroyAPIView):
    """分享链接详情视图"""
    serializer_class = ShareLinkSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        return ShareLink.objects.filter(
            created_by=self.request.user
        ).select_related('password_entry')
    
    def perform_update(self, serializer):
        """更新分享链接"""
        instance = serializer.save()
        
        # 记录操作日志
        OperationLog.objects.create(
            user=self.request.user,
            action_type='password_share',
            target_type='share_link',
            target_id=str(instance.id),
            description=f'更新分享链接: {instance.password_entry.title}',
            ip_address=self.request.META.get('REMOTE_ADDR', ''),
            user_agent=self.request.META.get('HTTP_USER_AGENT', ''),
            extra_data={
                'token': instance.token,
                'expires_at': instance.expires_at.isoformat() if instance.expires_at else None,
                'max_access_count': instance.max_access_count,
                'is_active': instance.is_active
            }
        )
    
    def perform_destroy(self, instance):
        """删除分享链接（软删除）"""
        instance.is_active = False
        instance.save()
        
        # 记录操作日志
        OperationLog.objects.create(
            user=self.request.user,
            action_type='share_revoke',
            target_type='share_link',
            target_id=str(instance.id),
            description=f'删除分享链接: {instance.password_entry.title}',
            ip_address=self.request.META.get('REMOTE_ADDR', ''),
            user_agent=self.request.META.get('HTTP_USER_AGENT', ''),
            extra_data={
                'token': instance.token,
                'password_entry_title': instance.password_entry.title
            }
        )


@api_view(['GET', 'POST'])
@permission_classes([permissions.AllowAny])
def share_link_access(request, token):
    """分享链接访问接口"""
    share_link = get_object_or_404(ShareLink, token=token)
    
    if request.method == 'GET':
        # 获取分享链接信息（不包含密码）
        return Response({
            'title': share_link.password_entry.title,
            'username': share_link.password_entry.username,
            'url': share_link.password_entry.url,
            'require_password': share_link.require_password,
            'is_expired': share_link.is_expired(),
            'is_active': share_link.is_active,
        })
    
    elif request.method == 'POST':
        # 访问分享链接获取密码
        serializer = ShareLinkAccessSerializer(
            data=request.data,
            context={'share_link': share_link}
        )
        
        if serializer.is_valid():
            # 增加访问次数
            share_link.access_count += 1
            share_link.save()
            
            # 记录访问日志
            AccessLog.objects.create(
                user=None,  # 匿名访问
                resource_type='share_link',
                resource_id=share_link.id,
                action='access_share_link',
                ip_address=request.META.get('REMOTE_ADDR'),
                user_agent=request.META.get('HTTP_USER_AGENT', ''),
                details={
                    'token': share_link.token,
                    'password_entry_title': share_link.password_entry.title
                }
            )
            
            # 返回密码信息
            password_serializer = ShareLinkPasswordSerializer(share_link.password_entry)
            return Response(password_serializer.data)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class ShareLinkStatsView(APIView):
    """分享链接统计视图"""
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request, pk):
        """获取分享链接统计信息"""
        share_link = get_object_or_404(
            ShareLink,
            pk=pk,
            created_by=request.user
        )
        
        # 获取访问日志统计
        access_logs = AccessLog.objects.filter(
            resource_type='share_link',
            resource_id=share_link.id
        )
        
        stats = {
            'total_access_count': share_link.access_count,
            'remaining_access_count': (
                share_link.max_access_count - share_link.access_count
                if share_link.max_access_count else None
            ),
            'is_expired': share_link.is_expired(),
            'created_at': share_link.created_at,
            'expires_at': share_link.expires_at,
            'recent_accesses': [
                {
                    'timestamp': log.timestamp,
                    'ip_address': log.ip_address,
                    'user_agent': log.user_agent
                }
                for log in access_logs.order_by('-timestamp')[:10]
            ]
        }
        
        return Response(stats)