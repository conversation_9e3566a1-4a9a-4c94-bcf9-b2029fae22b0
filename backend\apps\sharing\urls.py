from django.urls import path
from . import views

# 共享密码相关URL
shared_password_urlpatterns = [
    path('shared-passwords/', views.SharedPasswordListCreateView.as_view(), name='shared_password_list_create'),
    path('shared-passwords/<int:pk>/', views.SharedPasswordDetailView.as_view(), name='shared_password_detail'),
    path('received-passwords/', views.ReceivedSharedPasswordListView.as_view(), name='received_shared_password_list'),
    path('shared-passwords/<int:pk>/access/', views.SharedPasswordAccessView.as_view(), name='shared_password_access'),
]

# 分享链接相关URL
share_link_urlpatterns = [
    path('share-links/', views.ShareLinkListCreateView.as_view(), name='share_link_list_create'),
    path('share-links/<int:pk>/', views.ShareLinkDetailView.as_view(), name='share_link_detail'),
    path('share-links/<int:pk>/stats/', views.ShareLinkStatsView.as_view(), name='share_link_stats'),
]

# 公开访问URL（无需认证）
public_urlpatterns = [
    path('share/<str:token>/', views.share_link_access, name='share_link_access'),
]

urlpatterns = shared_password_urlpatterns + share_link_urlpatterns + public_urlpatterns