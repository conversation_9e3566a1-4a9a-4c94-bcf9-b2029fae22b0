from rest_framework import serializers
from django.contrib.auth import get_user_model
from .models import OperationLog, AccessLog, SecurityEvent

User = get_user_model()


class OperationLogSerializer(serializers.ModelSerializer):
    """操作日志序列化器"""
    user_name = serializers.Char<PERSON>ield(source='user.get_full_name', read_only=True)
    user_email = serializers.CharField(source='user.email', read_only=True)
    action_display = serializers.CharField(source='get_action_display', read_only=True)
    resource_type_display = serializers.CharField(source='get_resource_type_display', read_only=True)
    
    class Meta:
        model = OperationLog
        fields = [
            'id', 'user', 'user_name', 'user_email',
            'action', 'action_display',
            'resource_type', 'resource_type_display',
            'resource_id', 'details',
            'ip_address', 'user_agent',
            'timestamp', 'success'
        ]
        read_only_fields = ['id', 'timestamp']


class AccessLogSerializer(serializers.ModelSerializer):
    """访问日志序列化器"""
    user_name = serializers.Char<PERSON>ield(source='user.get_full_name', read_only=True)
    user_email = serializers.Char<PERSON>ield(source='user.email', read_only=True)
    action_display = serializers.CharField(source='get_action_display', read_only=True)
    resource_type_display = serializers.CharField(source='get_resource_type_display', read_only=True)
    
    class Meta:
        model = AccessLog
        fields = [
            'id', 'user', 'user_name', 'user_email',
            'resource_type', 'resource_type_display',
            'resource_id', 'action', 'action_display',
            'ip_address', 'user_agent',
            'timestamp', 'details'
        ]
        read_only_fields = ['id', 'timestamp']


class SecurityEventSerializer(serializers.ModelSerializer):
    """安全事件序列化器"""
    user_name = serializers.CharField(source='user.get_full_name', read_only=True)
    user_email = serializers.CharField(source='user.email', read_only=True)
    event_type_display = serializers.CharField(source='get_event_type_display', read_only=True)
    severity_display = serializers.CharField(source='get_severity_display', read_only=True)
    
    class Meta:
        model = SecurityEvent
        fields = [
            'id', 'user', 'user_name', 'user_email',
            'event_type', 'event_type_display',
            'severity', 'severity_display',
            'description', 'details',
            'ip_address', 'user_agent',
            'timestamp', 'resolved',
            'resolved_at', 'resolved_by'
        ]
        read_only_fields = ['id', 'timestamp']


class AuditStatsSerializer(serializers.Serializer):
    """审计统计序列化器"""
    total_operations = serializers.IntegerField(read_only=True)
    total_accesses = serializers.IntegerField(read_only=True)
    total_security_events = serializers.IntegerField(read_only=True)
    unresolved_security_events = serializers.IntegerField(read_only=True)
    
    # 按时间统计
    operations_by_hour = serializers.ListField(
        child=serializers.DictField(),
        read_only=True
    )
    operations_by_day = serializers.ListField(
        child=serializers.DictField(),
        read_only=True
    )
    
    # 按用户统计
    top_active_users = serializers.ListField(
        child=serializers.DictField(),
        read_only=True
    )
    
    # 按操作类型统计
    operations_by_action = serializers.ListField(
        child=serializers.DictField(),
        read_only=True
    )
    
    # 按资源类型统计
    operations_by_resource = serializers.ListField(
        child=serializers.DictField(),
        read_only=True
    )
    
    # 安全事件统计
    security_events_by_type = serializers.ListField(
        child=serializers.DictField(),
        read_only=True
    )
    security_events_by_severity = serializers.ListField(
        child=serializers.DictField(),
        read_only=True
    )


class UserActivitySerializer(serializers.Serializer):
    """用户活动序列化器"""
    user_id = serializers.IntegerField()
    user_name = serializers.CharField()
    user_email = serializers.CharField()
    
    # 操作统计
    total_operations = serializers.IntegerField()
    last_operation_time = serializers.DateTimeField()
    
    # 访问统计
    total_accesses = serializers.IntegerField()
    last_access_time = serializers.DateTimeField()
    
    # 安全事件统计
    security_events_count = serializers.IntegerField()
    last_security_event_time = serializers.DateTimeField(allow_null=True)
    
    # 最近活动
    recent_operations = OperationLogSerializer(many=True, read_only=True)
    recent_accesses = AccessLogSerializer(many=True, read_only=True)


class AuditSearchSerializer(serializers.Serializer):
    """审计搜索序列化器"""
    log_type = serializers.ChoiceField(
        choices=[('operation', '操作日志'), ('access', '访问日志'), ('security', '安全事件')],
        required=False,
        help_text="日志类型"
    )
    
    user_id = serializers.IntegerField(
        required=False,
        help_text="用户ID"
    )
    
    action = serializers.CharField(
        required=False,
        max_length=50,
        help_text="操作类型"
    )
    
    resource_type = serializers.CharField(
        required=False,
        max_length=50,
        help_text="资源类型"
    )
    
    resource_id = serializers.IntegerField(
        required=False,
        help_text="资源ID"
    )
    
    start_time = serializers.DateTimeField(
        required=False,
        help_text="开始时间"
    )
    
    end_time = serializers.DateTimeField(
        required=False,
        help_text="结束时间"
    )
    
    ip_address = serializers.IPAddressField(
        required=False,
        help_text="IP地址"
    )
    
    keyword = serializers.CharField(
        required=False,
        max_length=100,
        help_text="关键词搜索"
    )
    
    def validate(self, attrs):
        """验证搜索参数"""
        start_time = attrs.get('start_time')
        end_time = attrs.get('end_time')
        
        if start_time and end_time and start_time >= end_time:
            raise serializers.ValidationError("开始时间必须早于结束时间")
        
        return attrs


class SecurityEventCreateSerializer(serializers.ModelSerializer):
    """安全事件创建序列化器"""
    
    class Meta:
        model = SecurityEvent
        fields = [
            'user', 'event_type', 'severity',
            'description', 'details',
            'ip_address', 'user_agent'
        ]
    
    def validate_severity(self, value):
        """验证严重程度"""
        if value not in ['low', 'medium', 'high', 'critical']:
            raise serializers.ValidationError("无效的严重程度")
        return value


class SecurityEventResolveSerializer(serializers.Serializer):
    """安全事件解决序列化器"""
    resolution_notes = serializers.CharField(
        required=False,
        help_text="解决说明"
    )
    
    def update(self, instance, validated_data):
        """标记安全事件为已解决"""
        instance.resolved = True
        instance.resolved_at = timezone.now()
        instance.resolved_by = self.context['request'].user
        
        # 添加解决说明到详情中
        if validated_data.get('resolution_notes'):
            if not instance.details:
                instance.details = {}
            instance.details['resolution_notes'] = validated_data['resolution_notes']
        
        instance.save()
        return instance


class AuditExportSerializer(serializers.Serializer):
    """审计导出序列化器"""
    log_type = serializers.ChoiceField(
        choices=[('operation', '操作日志'), ('access', '访问日志'), ('security', '安全事件')],
        help_text="日志类型"
    )
    
    format = serializers.ChoiceField(
        choices=[('csv', 'CSV'), ('excel', 'Excel'), ('json', 'JSON')],
        default='csv',
        help_text="导出格式"
    )
    
    start_time = serializers.DateTimeField(
        required=False,
        help_text="开始时间"
    )
    
    end_time = serializers.DateTimeField(
        required=False,
        help_text="结束时间"
    )
    
    user_ids = serializers.ListField(
        child=serializers.IntegerField(),
        required=False,
        help_text="用户ID列表"
    )
    
    include_details = serializers.BooleanField(
        default=True,
        help_text="是否包含详细信息"
    )
    
    def validate(self, attrs):
        """验证导出参数"""
        start_time = attrs.get('start_time')
        end_time = attrs.get('end_time')
        
        if start_time and end_time and start_time >= end_time:
            raise serializers.ValidationError("开始时间必须早于结束时间")
        
        return attrs