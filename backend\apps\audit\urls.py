from django.urls import path
from rest_framework.routers import DefaultRouter
from . import views

# 操作日志相关URL
operation_log_urlpatterns = [
    path('operation-logs/', views.OperationLogListView.as_view(), name='operation_log_list'),
]

# 访问日志相关URL
access_log_urlpatterns = [
    path('access-logs/', views.AccessLogListView.as_view(), name='access_log_list'),
]

# 安全事件相关URL
security_event_urlpatterns = [
    path('security-events/', views.SecurityEventListCreateView.as_view(), name='security_event_list_create'),
    path('security-events/<int:pk>/', views.SecurityEventDetailView.as_view(), name='security_event_detail'),
    path('security-events/<int:pk>/resolve/', views.SecurityEventResolveView.as_view(), name='security_event_resolve'),
]

# 统计和分析相关URL
stats_urlpatterns = [
    path('stats/', views.AuditStatsView.as_view(), name='audit_stats'),
    path('user-activity/', views.UserActivityView.as_view(), name='user_activity_list'),
    path('user-activity/<int:user_id>/', views.UserActivityView.as_view(), name='user_activity_detail'),
]

# 导出相关URL
export_urlpatterns = [
    path('export/', views.AuditExportView.as_view(), name='audit_export'),
]

urlpatterns = (
    operation_log_urlpatterns +
    access_log_urlpatterns +
    security_event_urlpatterns +
    stats_urlpatterns +
    export_urlpatterns
)

# 使用DRF路由器
router = DefaultRouter()
# router.register(r'logs', AuditLogViewSet)

urlpatterns += router.urls