from rest_framework import status, generics, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework_simplejwt.views import TokenRefreshView
from django.contrib.auth.tokens import default_token_generator
from django.contrib.auth import logout
from django.core.mail import send_mail
from django.conf import settings
from django.utils.http import urlsafe_base64_encode, urlsafe_base64_decode
from django.utils.encoding import force_bytes, force_str
from django.utils.translation import gettext_lazy as _
from django.template.loader import render_to_string
from django.urls import reverse
from .models import User, Department, Team, Role
from .serializers import (
    UserSerializer,
    UserCreateSerializer,
    UserUpdateSerializer,
    LoginSerializer,
    PasswordChangeSerializer,
    PasswordResetSerializer,
    PasswordResetConfirmSerializer,
    MFASetupSerializer,
    MFAQRCodeSerializer,
    DepartmentSerializer,
    TeamSerializer,
    RoleSerializer,
)
from apps.audit.models import LoginLog, OperationLog
import logging

logger = logging.getLogger(__name__)


class LoginView(APIView):
    """用户登录视图"""

    permission_classes = [permissions.AllowAny]

    def post(self, request):
        serializer = LoginSerializer(data=request.data, context={"request": request})

        if serializer.is_valid():
            user = serializer.validated_data["user"]

            # 生成JWT令牌
            refresh = RefreshToken.for_user(user)
            access_token = refresh.access_token

            # 记录登录日志
            try:
                LoginLog.objects.create(
                    user=user,
                    username=user.username,
                    ip_address=self.get_client_ip(request),
                    user_agent=request.META.get("HTTP_USER_AGENT", ""),
                    login_type="password",
                    result="success",
                )
            except Exception as e:
                logger.error(f"记录登录日志失败: {e}")

            # 记录操作日志
            try:
                OperationLog.objects.create(
                    user=user,
                    action_type="user_login",
                    target_type="user",
                    target_id=str(user.id),
                    ip_address=self.get_client_ip(request),
                    user_agent=request.META.get("HTTP_USER_AGENT", ""),
                    extra_data={"login_method": "password"},
                )
            except Exception as e:
                logger.error(f"记录操作日志失败: {e}")

            return Response(
                {
                    "access_token": str(access_token),
                    "refresh_token": str(refresh),
                    "user": UserSerializer(user).data,
                    "message": _("登录成功"),
                },
                status=status.HTTP_200_OK,
            )

        # 登录失败，记录失败日志
        username = request.data.get("username")
        email = request.data.get("email")
        login_identifier = username or email

        if login_identifier:
            try:
                if email:
                    user = User.objects.get(email=email)
                else:
                    user = User.objects.get(username=username)
                LoginLog.objects.create(
                    user=user,
                    username=user.username,
                    ip_address=self.get_client_ip(request),
                    user_agent=request.META.get("HTTP_USER_AGENT", ""),
                    login_type="password",
                    result="failed_password",
                    failure_reason=str(serializer.errors),
                )
            except User.DoesNotExist:
                LoginLog.objects.create(
                    username=login_identifier,
                    ip_address=self.get_client_ip(request),
                    user_agent=request.META.get("HTTP_USER_AGENT", ""),
                    login_type="password",
                    result="failed_other",
                    failure_reason=f"用户不存在: {login_identifier}",
                )
            except Exception as e:
                logger.error(f"记录登录失败日志失败: {e}")

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
        if x_forwarded_for:
            ip = x_forwarded_for.split(",")[0]
        else:
            ip = request.META.get("REMOTE_ADDR")
        return ip


class LogoutView(APIView):
    """用户登出视图"""

    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        try:
            # 记录登出日志
            OperationLog.objects.create(
                user=request.user,
                action="logout",
                resource_type="user",
                resource_id=str(request.user.id),
                ip_address=self.get_client_ip(request),
                user_agent=request.META.get("HTTP_USER_AGENT", ""),
            )

            # 将刷新令牌加入黑名单
            refresh_token = request.data.get("refresh_token")
            if refresh_token:
                token = RefreshToken(refresh_token)
                token.blacklist()

            return Response({"message": _("登出成功")}, status=status.HTTP_200_OK)
        except Exception as e:
            logger.error(f"登出失败: {e}")
            return Response(
                {"error": _("登出失败")}, status=status.HTTP_400_BAD_REQUEST
            )

    def get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
        if x_forwarded_for:
            ip = x_forwarded_for.split(",")[0]
        else:
            ip = request.META.get("REMOTE_ADDR")
        return ip


class CustomTokenRefreshView(TokenRefreshView):
    """自定义令牌刷新视图"""

    def post(self, request, *args, **kwargs):
        response = super().post(request, *args, **kwargs)

        if response.status_code == 200:
            # 记录令牌刷新日志
            try:
                OperationLog.objects.create(
                    user=request.user if request.user.is_authenticated else None,
                    action="token_refresh",
                    resource_type="token",
                    ip_address=self.get_client_ip(request),
                    user_agent=request.META.get("HTTP_USER_AGENT", ""),
                )
            except Exception as e:
                logger.error(f"记录令牌刷新日志失败: {e}")

        return response

    def get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
        if x_forwarded_for:
            ip = x_forwarded_for.split(",")[0]
        else:
            ip = request.META.get("REMOTE_ADDR")
        return ip


class PasswordChangeView(APIView):
    """密码修改视图"""

    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        serializer = PasswordChangeSerializer(
            data=request.data, context={"request": request}
        )

        if serializer.is_valid():
            serializer.save()

            # 记录密码修改日志
            try:
                OperationLog.objects.create(
                    user=request.user,
                    action="password_change",
                    resource_type="user",
                    resource_id=str(request.user.id),
                    ip_address=self.get_client_ip(request),
                    user_agent=request.META.get("HTTP_USER_AGENT", ""),
                )
            except Exception as e:
                logger.error(f"记录密码修改日志失败: {e}")

            return Response({"message": _("密码修改成功")}, status=status.HTTP_200_OK)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
        if x_forwarded_for:
            ip = x_forwarded_for.split(",")[0]
        else:
            ip = request.META.get("REMOTE_ADDR")
        return ip


class PasswordResetView(APIView):
    """密码重置请求视图"""

    permission_classes = [permissions.AllowAny]

    def post(self, request):
        serializer = PasswordResetSerializer(data=request.data)

        if serializer.is_valid():
            user = serializer.user

            # 生成重置令牌
            token = default_token_generator.make_token(user)
            uid = urlsafe_base64_encode(force_bytes(user.pk))

            # 构建重置链接
            reset_url = request.build_absolute_uri(
                reverse(
                    "password_reset_confirm", kwargs={"uidb64": uid, "token": token}
                )
            )

            # 发送重置邮件
            try:
                subject = _("密码重置请求")
                message = render_to_string(
                    "emails/password_reset.html",
                    {
                        "user": user,
                        "reset_url": reset_url,
                        "site_name": "Password Locker",
                    },
                )

                send_mail(
                    subject,
                    message,
                    settings.DEFAULT_FROM_EMAIL,
                    [user.email],
                    html_message=message,
                )

                # 记录操作日志
                OperationLog.objects.create(
                    user=user,
                    action="password_reset_request",
                    resource_type="user",
                    resource_id=str(user.id),
                    ip_address=self.get_client_ip(request),
                    user_agent=request.META.get("HTTP_USER_AGENT", ""),
                )

                return Response(
                    {"message": _("密码重置邮件已发送，请检查您的邮箱")},
                    status=status.HTTP_200_OK,
                )

            except Exception as e:
                logger.error(f"发送密码重置邮件失败: {e}")
                return Response(
                    {"error": _("发送邮件失败，请稍后再试")},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                )

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
        if x_forwarded_for:
            ip = x_forwarded_for.split(",")[0]
        else:
            ip = request.META.get("REMOTE_ADDR")
        return ip


class PasswordResetConfirmView(APIView):
    """密码重置确认视图"""

    permission_classes = [permissions.AllowAny]

    def post(self, request, uidb64, token):
        try:
            uid = force_str(urlsafe_base64_decode(uidb64))
            user = User.objects.get(pk=uid)
        except (TypeError, ValueError, OverflowError, User.DoesNotExist):
            return Response(
                {"error": _("无效的重置链接")}, status=status.HTTP_400_BAD_REQUEST
            )

        if not default_token_generator.check_token(user, token):
            return Response(
                {"error": _("重置链接已过期或无效")}, status=status.HTTP_400_BAD_REQUEST
            )

        serializer = PasswordResetConfirmSerializer(data=request.data)

        if serializer.is_valid():
            user.set_password(serializer.validated_data["new_password"])
            user.save()

            # 记录密码重置日志
            try:
                OperationLog.objects.create(
                    user=user,
                    action="password_reset_confirm",
                    resource_type="user",
                    resource_id=str(user.id),
                    ip_address=self.get_client_ip(request),
                    user_agent=request.META.get("HTTP_USER_AGENT", ""),
                )
            except Exception as e:
                logger.error(f"记录密码重置日志失败: {e}")

            return Response({"message": _("密码重置成功")}, status=status.HTTP_200_OK)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
        if x_forwarded_for:
            ip = x_forwarded_for.split(",")[0]
        else:
            ip = request.META.get("REMOTE_ADDR")
        return ip


class MFASetupView(APIView):
    """MFA设置视图"""

    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        serializer = MFASetupSerializer(data=request.data, context={"request": request})

        if serializer.is_valid():
            user = serializer.save()

            # 记录MFA设置日志
            try:
                action = "mfa_enable" if user.is_mfa_enabled else "mfa_disable"
                OperationLog.objects.create(
                    user=request.user,
                    action=action,
                    resource_type="user",
                    resource_id=str(request.user.id),
                    ip_address=self.get_client_ip(request),
                    user_agent=request.META.get("HTTP_USER_AGENT", ""),
                )
            except Exception as e:
                logger.error(f"记录MFA设置日志失败: {e}")

            message = (
                _("多因素认证已启用") if user.is_mfa_enabled else _("多因素认证已禁用")
            )
            return Response({"message": message}, status=status.HTTP_200_OK)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
        if x_forwarded_for:
            ip = x_forwarded_for.split(",")[0]
        else:
            ip = request.META.get("REMOTE_ADDR")
        return ip


class MFAQRCodeView(APIView):
    """MFA二维码视图"""

    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        serializer = MFAQRCodeSerializer(context={"request": request})
        return Response(serializer.to_representation(None), status=status.HTTP_200_OK)


class UserProfileView(APIView):
    """用户个人资料视图"""

    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        serializer = UserSerializer(request.user)
        return Response(serializer.data, status=status.HTTP_200_OK)


class AccessCodesView(APIView):
    """获取用户权限码视图"""

    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        # 根据用户角色返回权限码
        user = request.user
        access_codes = []

        # 基础权限
        access_codes.append("user:read")

        # 根据角色添加权限
        if user.role:
            if user.role.name == "admin":
                access_codes.extend(
                    [
                        "user:create",
                        "user:update",
                        "user:delete",
                        "password:create",
                        "password:read",
                        "password:update",
                        "password:delete",
                        "category:create",
                        "category:read",
                        "category:update",
                        "category:delete",
                        "system:read",
                        "system:update",
                    ]
                )
            elif user.role.name == "manager":
                access_codes.extend(
                    [
                        "password:create",
                        "password:read",
                        "password:update",
                        "password:delete",
                        "category:create",
                        "category:read",
                        "category:update",
                        "category:delete",
                    ]
                )
            elif user.role.name == "user":
                access_codes.extend(
                    ["password:read", "password:create", "password:update"]
                )
        else:
            # 如果用户没有角色，给予基本的密码管理权限
            access_codes.extend(["password:read", "password:create", "password:update"])

        return Response(access_codes, status=status.HTTP_200_OK)


class UserProfileUpdateView(APIView):
    """用户个人资料更新视图"""

    permission_classes = [permissions.IsAuthenticated]

    def put(self, request):
        serializer = UserUpdateSerializer(request.user, data=request.data, partial=True)

        if serializer.is_valid():
            serializer.save()

            # 记录用户信息更新日志
            try:
                OperationLog.objects.create(
                    user=request.user,
                    action="profile_update",
                    resource_type="user",
                    resource_id=str(request.user.id),
                    ip_address=self.get_client_ip(request),
                    user_agent=request.META.get("HTTP_USER_AGENT", ""),
                    details={"updated_fields": list(serializer.validated_data.keys())},
                )
            except Exception as e:
                logger.error(f"记录用户信息更新日志失败: {e}")

            return Response(
                UserSerializer(request.user).data, status=status.HTTP_200_OK
            )

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
        if x_forwarded_for:
            ip = x_forwarded_for.split(",")[0]
        else:
            ip = request.META.get("REMOTE_ADDR")
        return ip


# 用户管理视图（管理员功能）
class UserListCreateView(generics.ListCreateAPIView):
    """用户列表和创建视图"""

    queryset = User.objects.all()
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_class(self):
        if self.request.method == "POST":
            return UserCreateSerializer
        return UserSerializer

    def perform_create(self, serializer):
        user = serializer.save()

        # 记录用户创建日志
        try:
            OperationLog.objects.create(
                user=self.request.user,
                action_type="user_create",
                target_type="user",
                target_id=str(user.id),
                ip_address=self.get_client_ip(self.request),
                user_agent=self.request.META.get("HTTP_USER_AGENT", ""),
                extra_data={"created_user": user.username},
            )
        except Exception as e:
            logger.error(f"记录用户创建日志失败: {e}")

    def get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
        if x_forwarded_for:
            ip = x_forwarded_for.split(",")[0]
        else:
            ip = request.META.get("REMOTE_ADDR")
        return ip


class UserDetailView(generics.RetrieveUpdateDestroyAPIView):
    """用户详情视图"""

    queryset = User.objects.all()
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_class(self):
        """根据请求方法返回不同的序列化器"""
        if self.request.method == "GET":
            return UserSerializer
        return UserUpdateSerializer

    def perform_update(self, serializer):
        user = serializer.save()

        # 记录用户更新日志
        try:
            OperationLog.objects.create(
                user=self.request.user,
                action="user_update",
                resource_type="user",
                resource_id=str(user.id),
                ip_address=self.get_client_ip(self.request),
                user_agent=self.request.META.get("HTTP_USER_AGENT", ""),
                details={
                    "updated_user": user.username,
                    "updated_fields": list(serializer.validated_data.keys()),
                },
            )
        except Exception as e:
            logger.error(f"记录用户更新日志失败: {e}")

    def perform_destroy(self, instance):
        # 记录用户删除日志
        try:
            OperationLog.objects.create(
                user=self.request.user,
                action_type="user_delete",
                target_type="user",
                target_id=str(instance.id),
                ip_address=self.get_client_ip(self.request),
                user_agent=self.request.META.get("HTTP_USER_AGENT", ""),
                extra_data={"deleted_user": instance.username},
            )
        except Exception as e:
            logger.error(f"记录用户删除日志失败: {e}")

        instance.delete()

    def get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
        if x_forwarded_for:
            ip = x_forwarded_for.split(",")[0]
        else:
            ip = request.META.get("REMOTE_ADDR")
        return ip


# 部门管理视图
class DepartmentListCreateView(generics.ListCreateAPIView):
    """部门列表和创建视图"""

    queryset = Department.objects.all()
    serializer_class = DepartmentSerializer
    permission_classes = [permissions.IsAuthenticated]


class DepartmentDetailView(generics.RetrieveUpdateDestroyAPIView):
    """部门详情视图"""

    queryset = Department.objects.all()
    serializer_class = DepartmentSerializer
    permission_classes = [permissions.IsAuthenticated]


# 团队管理视图
class TeamListCreateView(generics.ListCreateAPIView):
    """团队列表和创建视图"""

    queryset = Team.objects.all()
    serializer_class = TeamSerializer
    permission_classes = [permissions.IsAuthenticated]


class TeamDetailView(generics.RetrieveUpdateDestroyAPIView):
    """团队详情视图"""

    queryset = Team.objects.all()
    serializer_class = TeamSerializer
    permission_classes = [permissions.IsAuthenticated]


# 角色管理视图
class RoleListCreateView(generics.ListCreateAPIView):
    """角色列表和创建视图"""

    queryset = Role.objects.all()
    serializer_class = RoleSerializer
    permission_classes = [permissions.IsAuthenticated]


class RoleDetailView(generics.RetrieveUpdateDestroyAPIView):
    """角色详情视图"""

    queryset = Role.objects.all()
    serializer_class = RoleSerializer
    permission_classes = [permissions.IsAuthenticated]
