from django.http import JsonResponse
from django.views import View
from django.urls import reverse
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def api_documentation(request):
    """
    API文档视图 - 返回所有可用的API端点
    """
    api_endpoints = {
        "认证相关API": {
            "登录": "/api/auth/login/",
            "登出": "/api/auth/logout/",
            "刷新令牌": "/api/auth/token/refresh/",
            "修改密码": "/api/auth/change-password/",
            "重置密码": "/api/auth/reset-password/",
            "确认重置密码": "/api/auth/reset-password/confirm/",
            "MFA设置": "/api/auth/mfa/setup/",
            "MFA验证": "/api/auth/mfa/verify/",
            "MFA禁用": "/api/auth/mfa/disable/",
            "用户资料": "/api/auth/profile/",
        },
        "用户管理API": {
            "用户列表": "/api/auth/users/",
            "用户详情": "/api/auth/users/{id}/",
            "部门列表": "/api/auth/departments/",
            "部门详情": "/api/auth/departments/{id}/",
            "团队列表": "/api/auth/teams/",
            "团队详情": "/api/auth/teams/{id}/",
            "角色列表": "/api/auth/roles/",
            "角色详情": "/api/auth/roles/{id}/",
        },
        "密码管理API": {
            "密码条目列表": "/api/passwords/entries/",
            "密码条目详情": "/api/passwords/entries/{id}/",
            "复制密码": "/api/passwords/entries/{id}/copy/",
            "密码生成器": "/api/passwords/generator/",
            "分类列表": "/api/passwords/categories/",
            "分类详情": "/api/passwords/categories/{id}/",
            "标签列表": "/api/passwords/tags/",
            "标签详情": "/api/passwords/tags/{id}/",
            "自定义字段列表": "/api/passwords/entries/{entry_id}/custom-fields/",
            "自定义字段详情": "/api/passwords/custom-fields/{id}/",
            "附件列表": "/api/passwords/entries/{entry_id}/attachments/",
            "附件详情": "/api/passwords/attachments/{id}/",
            "安全分析": "/api/passwords/security-analysis/",
        },
        "密码分享API": {
            "共享密码列表": "/api/sharing/shared-passwords/",
            "共享密码详情": "/api/sharing/shared-passwords/{id}/",
            "接收的共享密码": "/api/sharing/received-passwords/",
            "访问共享密码": "/api/sharing/shared-passwords/{id}/access/",
            "分享链接列表": "/api/sharing/share-links/",
            "分享链接详情": "/api/sharing/share-links/{id}/",
            "分享链接访问": "/api/sharing/links/{token}/",
            "分享链接统计": "/api/sharing/share-links/{id}/stats/",
        },
        "审计日志API": {
            "操作日志": "/api/audit/operation-logs/",
            "访问日志": "/api/audit/access-logs/",
            "安全事件列表": "/api/audit/security-events/",
            "安全事件详情": "/api/audit/security-events/{id}/",
            "解决安全事件": "/api/audit/security-events/{id}/resolve/",
            "审计统计": "/api/audit/stats/",
            "用户活动": "/api/audit/user-activity/",
            "导出日志": "/api/audit/export/",
        },
        "系统管理API": {
            "系统设置列表": "/api/system/settings/",
            "系统设置详情": "/api/system/settings/{key}/",
            "批量更新设置": "/api/system/settings/batch/update/",
            "邮件模板列表": "/api/system/email-templates/",
            "邮件模板详情": "/api/system/email-templates/{id}/",
            "备份配置列表": "/api/system/backup-configs/",
            "备份配置详情": "/api/system/backup-configs/{id}/",
            "系统状态": "/api/system/status/",
            "系统维护": "/api/system/maintenance/",
        },
    }
    
    return Response({
        "message": "密码管理系统API文档",
        "version": "1.0.0",
        "base_url": request.build_absolute_uri('/'),
        "endpoints": api_endpoints,
        "authentication": {
            "type": "JWT Token",
            "header": "Authorization: Bearer <token>",
            "login_endpoint": "/api/auth/login/",
        },
        "notes": [
            "所有API都需要认证，除了登录、重置密码确认和分享链接访问接口",
            "使用JWT Token进行认证，在请求头中添加 Authorization: Bearer <token>",
            "分页参数: page, page_size",
            "搜索参数: search, ordering",
            "时间格式: ISO 8601 (YYYY-MM-DDTHH:MM:SSZ)",
        ]
    })