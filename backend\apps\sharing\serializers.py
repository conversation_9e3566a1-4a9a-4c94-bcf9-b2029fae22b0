from rest_framework import serializers
from django.contrib.auth import get_user_model
from django.utils import timezone
from .models import SharedPassword, ShareLink
from apps.passwords.models import PasswordEntry
from apps.passwords.serializers import PasswordEntrySerializer
from utils.encryption import decrypt_password

User = get_user_model()


class SharedPasswordSerializer(serializers.ModelSerializer):
    """共享密码序列化器"""
    shared_by_name = serializers.Char<PERSON>ield(source='shared_by.get_full_name', read_only=True)
    shared_with_name = serializers.CharField(source='shared_with.get_full_name', read_only=True)
    password_entry_title = serializers.CharField(source='password_entry.title', read_only=True)
    password_entry_username = serializers.CharField(source='password_entry.username', read_only=True)
    password_entry_url = serializers.CharField(source='password_entry.url', read_only=True)
    
    class Meta:
        model = SharedPassword
        fields = [
            'id', 'password_entry', 'shared_by', 'shared_with',
            'permission_level', 'expires_at', 'is_active',
            'created_at', 'updated_at',
            'shared_by_name', 'shared_with_name',
            'password_entry_title', 'password_entry_username', 'password_entry_url'
        ]
        read_only_fields = ['id', 'shared_by', 'created_at', 'updated_at']
    
    def validate_expires_at(self, value):
        """验证过期时间"""
        if value and value <= timezone.now():
            raise serializers.ValidationError("过期时间必须是未来时间")
        return value
    
    def validate(self, attrs):
        """验证数据"""
        password_entry = attrs.get('password_entry')
        shared_with = attrs.get('shared_with')
        
        # 检查是否已经分享给该用户
        if SharedPassword.objects.filter(
            password_entry=password_entry,
            shared_with=shared_with,
            is_active=True
        ).exists():
            raise serializers.ValidationError("已经分享给该用户")
        
        # 不能分享给自己
        request = self.context.get('request')
        if request and shared_with == request.user:
            raise serializers.ValidationError("不能分享给自己")
        
        return attrs


class SharedPasswordCreateSerializer(serializers.ModelSerializer):
    """创建共享密码序列化器"""
    shared_with_emails = serializers.ListField(
        child=serializers.EmailField(),
        write_only=True,
        help_text="分享给的用户邮箱列表"
    )
    
    class Meta:
        model = SharedPassword
        fields = [
            'password_entry', 'shared_with_emails', 'permission_level', 'expires_at'
        ]
    
    def validate_shared_with_emails(self, value):
        """验证邮箱列表"""
        if not value:
            raise serializers.ValidationError("至少需要一个邮箱地址")
        
        # 检查用户是否存在
        existing_emails = set(User.objects.filter(email__in=value).values_list('email', flat=True))
        non_existing_emails = set(value) - existing_emails
        
        if non_existing_emails:
            raise serializers.ValidationError(
                f"以下邮箱对应的用户不存在: {', '.join(non_existing_emails)}"
            )
        
        return value
    
    def create(self, validated_data):
        """创建多个共享记录"""
        shared_with_emails = validated_data.pop('shared_with_emails')
        users = User.objects.filter(email__in=shared_with_emails)
        
        shared_passwords = []
        for user in users:
            shared_password = SharedPassword.objects.create(
                shared_with=user,
                **validated_data
            )
            shared_passwords.append(shared_password)
        
        return shared_passwords


class ShareLinkSerializer(serializers.ModelSerializer):
    """分享链接序列化器"""
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    password_entry_title = serializers.CharField(source='password_entry.title', read_only=True)
    access_count = serializers.IntegerField(read_only=True)
    is_expired = serializers.SerializerMethodField()
    share_url = serializers.SerializerMethodField()
    
    class Meta:
        model = ShareLink
        fields = [
            'id', 'password_entry', 'token', 'expires_at', 'max_access_count',
            'access_count', 'require_password', 'access_password',
            'is_active', 'created_at', 'updated_at',
            'created_by_name', 'password_entry_title', 'is_expired', 'share_url'
        ]
        read_only_fields = ['id', 'token', 'created_by', 'created_at', 'updated_at']
        extra_kwargs = {
            'access_password': {'write_only': True}
        }
    
    def get_is_expired(self, obj):
        """检查是否过期"""
        return obj.is_expired()
    
    def get_share_url(self, obj):
        """获取分享链接"""
        request = self.context.get('request')
        if request:
            return request.build_absolute_uri(f'/share/{obj.token}/')
        return f'/share/{obj.token}/'
    
    def validate_expires_at(self, value):
        """验证过期时间"""
        if value and value <= timezone.now():
            raise serializers.ValidationError("过期时间必须是未来时间")
        return value
    
    def validate_max_access_count(self, value):
        """验证最大访问次数"""
        if value is not None and value <= 0:
            raise serializers.ValidationError("最大访问次数必须大于0")
        return value


class ShareLinkAccessSerializer(serializers.Serializer):
    """分享链接访问序列化器"""
    access_password = serializers.CharField(
        required=False,
        help_text="访问密码（如果需要）"
    )
    
    def validate(self, attrs):
        """验证访问权限"""
        share_link = self.context.get('share_link')
        
        if not share_link:
            raise serializers.ValidationError("分享链接不存在")
        
        # 检查链接是否有效
        if not share_link.is_active:
            raise serializers.ValidationError("分享链接已被禁用")
        
        if share_link.is_expired():
            raise serializers.ValidationError("分享链接已过期")
        
        # 检查访问次数
        if (share_link.max_access_count is not None and 
            share_link.access_count >= share_link.max_access_count):
            raise serializers.ValidationError("分享链接访问次数已达上限")
        
        # 检查访问密码
        if share_link.require_password:
            access_password = attrs.get('access_password')
            if not access_password:
                raise serializers.ValidationError("需要提供访问密码")
            
            if not share_link.check_access_password(access_password):
                raise serializers.ValidationError("访问密码错误")
        
        return attrs


class SharedPasswordDetailSerializer(serializers.ModelSerializer):
    """共享密码详情序列化器（包含解密后的密码）"""
    password_entry = PasswordEntrySerializer(read_only=True)
    shared_by_name = serializers.CharField(source='shared_by.get_full_name', read_only=True)
    decrypted_password = serializers.SerializerMethodField()
    
    class Meta:
        model = SharedPassword
        fields = [
            'id', 'password_entry', 'shared_by', 'shared_by_name',
            'permission_level', 'expires_at', 'is_active',
            'created_at', 'updated_at', 'decrypted_password'
        ]
    
    def get_decrypted_password(self, obj):
        """获取解密后的密码"""
        try:
            return decrypt_password(obj.password_entry.encrypted_password)
        except Exception:
            return None


class ShareLinkPasswordSerializer(serializers.Serializer):
    """分享链接密码序列化器"""
    title = serializers.CharField(read_only=True)
    username = serializers.CharField(read_only=True)
    password = serializers.CharField(read_only=True)
    url = serializers.CharField(read_only=True)
    notes = serializers.CharField(read_only=True)
    
    def to_representation(self, instance):
        """自定义序列化输出"""
        try:
            decrypted_password = decrypt_password(instance.encrypted_password)
        except Exception:
            decrypted_password = None
        
        return {
            'title': instance.title,
            'username': instance.username,
            'password': decrypted_password,
            'url': instance.url,
            'notes': instance.notes,
        }