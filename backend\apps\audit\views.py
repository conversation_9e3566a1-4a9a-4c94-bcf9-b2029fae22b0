from rest_framework import generics, permissions, status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.views import APIView
from django.shortcuts import get_object_or_404
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.db.models import Count, Q
from django.http import HttpResponse
from django.core.paginator import Paginator
from datetime import datetime, timedelta
import csv
import json
import io
# from openpyxl import Workbook
# from openpyxl.utils.dataframe import dataframe_to_rows
# import pandas as pd

from .models import OperationLog, AccessLog, SecurityEvent
from .serializers import (
    OperationLogSerializer,
    AccessLogSerializer,
    SecurityEventSerializer,
    AuditStatsSerializer,
    UserActivitySerializer,
    AuditSearchSerializer,
    SecurityEventCreateSerializer,
    SecurityEventResolveSerializer,
    AuditExportSerializer
)
from utils.permissions import IsAdminOrSuperUser

User = get_user_model()


class OperationLogListView(generics.ListAPIView):
    """操作日志列表视图"""
    serializer_class = OperationLogSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """获取操作日志查询集"""
        queryset = OperationLog.objects.select_related('user').order_by('-timestamp')
        
        # 非管理员只能查看自己的日志
        if not self.request.user.is_staff:
            queryset = queryset.filter(user=self.request.user)
        
        # 应用搜索过滤器
        return self.apply_filters(queryset)
    
    def apply_filters(self, queryset):
        """应用搜索过滤器"""
        user_id = self.request.query_params.get('user_id')
        action = self.request.query_params.get('action')
        resource_type = self.request.query_params.get('resource_type')
        resource_id = self.request.query_params.get('resource_id')
        start_time = self.request.query_params.get('start_time')
        end_time = self.request.query_params.get('end_time')
        keyword = self.request.query_params.get('keyword')
        
        if user_id:
            queryset = queryset.filter(user_id=user_id)
        
        if action:
            queryset = queryset.filter(action=action)
        
        if resource_type:
            queryset = queryset.filter(resource_type=resource_type)
        
        if resource_id:
            queryset = queryset.filter(resource_id=resource_id)
        
        if start_time:
            try:
                start_dt = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
                queryset = queryset.filter(timestamp__gte=start_dt)
            except ValueError:
                pass
        
        if end_time:
            try:
                end_dt = datetime.fromisoformat(end_time.replace('Z', '+00:00'))
                queryset = queryset.filter(timestamp__lte=end_dt)
            except ValueError:
                pass
        
        if keyword:
            queryset = queryset.filter(
                Q(details__icontains=keyword) |
                Q(user__email__icontains=keyword) |
                Q(user__first_name__icontains=keyword) |
                Q(user__last_name__icontains=keyword)
            )
        
        return queryset


class AccessLogListView(generics.ListAPIView):
    """访问日志列表视图"""
    serializer_class = AccessLogSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """获取访问日志查询集"""
        queryset = AccessLog.objects.select_related('user').order_by('-timestamp')
        
        # 非管理员只能查看自己的日志
        if not self.request.user.is_staff:
            queryset = queryset.filter(user=self.request.user)
        
        # 应用搜索过滤器
        return self.apply_filters(queryset)
    
    def apply_filters(self, queryset):
        """应用搜索过滤器"""
        user_id = self.request.query_params.get('user_id')
        action = self.request.query_params.get('action')
        resource_type = self.request.query_params.get('resource_type')
        resource_id = self.request.query_params.get('resource_id')
        start_time = self.request.query_params.get('start_time')
        end_time = self.request.query_params.get('end_time')
        ip_address = self.request.query_params.get('ip_address')
        
        if user_id:
            queryset = queryset.filter(user_id=user_id)
        
        if action:
            queryset = queryset.filter(action=action)
        
        if resource_type:
            queryset = queryset.filter(resource_type=resource_type)
        
        if resource_id:
            queryset = queryset.filter(resource_id=resource_id)
        
        if start_time:
            try:
                start_dt = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
                queryset = queryset.filter(timestamp__gte=start_dt)
            except ValueError:
                pass
        
        if end_time:
            try:
                end_dt = datetime.fromisoformat(end_time.replace('Z', '+00:00'))
                queryset = queryset.filter(timestamp__lte=end_dt)
            except ValueError:
                pass
        
        if ip_address:
            queryset = queryset.filter(ip_address=ip_address)
        
        return queryset


class SecurityEventListCreateView(generics.ListCreateAPIView):
    """安全事件列表和创建视图"""
    permission_classes = [IsAdminOrSuperUser]
    
    def get_serializer_class(self):
        if self.request.method == 'POST':
            return SecurityEventCreateSerializer
        return SecurityEventSerializer
    
    def get_queryset(self):
        """获取安全事件查询集"""
        queryset = SecurityEvent.objects.select_related('user', 'resolved_by').order_by('-timestamp')
        
        # 应用过滤器
        event_type = self.request.query_params.get('event_type')
        severity = self.request.query_params.get('severity')
        resolved = self.request.query_params.get('resolved')
        user_id = self.request.query_params.get('user_id')
        start_time = self.request.query_params.get('start_time')
        end_time = self.request.query_params.get('end_time')
        
        if event_type:
            queryset = queryset.filter(event_type=event_type)
        
        if severity:
            queryset = queryset.filter(severity=severity)
        
        if resolved is not None:
            queryset = queryset.filter(resolved=resolved.lower() == 'true')
        
        if user_id:
            queryset = queryset.filter(user_id=user_id)
        
        if start_time:
            try:
                start_dt = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
                queryset = queryset.filter(timestamp__gte=start_dt)
            except ValueError:
                pass
        
        if end_time:
            try:
                end_dt = datetime.fromisoformat(end_time.replace('Z', '+00:00'))
                queryset = queryset.filter(timestamp__lte=end_dt)
            except ValueError:
                pass
        
        return queryset


class SecurityEventDetailView(generics.RetrieveUpdateAPIView):
    """安全事件详情视图"""
    queryset = SecurityEvent.objects.all()
    serializer_class = SecurityEventSerializer
    permission_classes = [IsAdminOrSuperUser]


class SecurityEventResolveView(APIView):
    """安全事件解决视图"""
    permission_classes = [IsAdminOrSuperUser]
    
    def post(self, request, pk):
        """标记安全事件为已解决"""
        security_event = get_object_or_404(SecurityEvent, pk=pk)
        
        if security_event.resolved:
            return Response(
                {'error': '安全事件已经被解决'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        serializer = SecurityEventResolveSerializer(
            security_event,
            data=request.data,
            context={'request': request}
        )
        
        if serializer.is_valid():
            serializer.save()
            return Response(SecurityEventSerializer(security_event).data)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class AuditStatsView(APIView):
    """审计统计视图"""
    permission_classes = [IsAdminOrSuperUser]
    
    def get(self, request):
        """获取审计统计信息"""
        # 时间范围
        days = int(request.query_params.get('days', 30))
        start_time = timezone.now() - timedelta(days=days)
        
        # 基础统计
        total_operations = OperationLog.objects.filter(timestamp__gte=start_time).count()
        total_accesses = AccessLog.objects.filter(timestamp__gte=start_time).count()
        total_security_events = SecurityEvent.objects.filter(timestamp__gte=start_time).count()
        unresolved_security_events = SecurityEvent.objects.filter(
            timestamp__gte=start_time,
            resolved=False
        ).count()
        
        # 按小时统计操作
        operations_by_hour = list(
            OperationLog.objects.filter(timestamp__gte=start_time)
            .extra({'hour': "date_trunc('hour', timestamp)"})
            .values('hour')
            .annotate(count=Count('id'))
            .order_by('hour')
        )
        
        # 按天统计操作
        operations_by_day = list(
            OperationLog.objects.filter(timestamp__gte=start_time)
            .extra({'day': "date_trunc('day', timestamp)"})
            .values('day')
            .annotate(count=Count('id'))
            .order_by('day')
        )
        
        # 最活跃用户
        top_active_users = list(
            OperationLog.objects.filter(timestamp__gte=start_time)
            .values('user__id', 'user__email', 'user__first_name', 'user__last_name')
            .annotate(count=Count('id'))
            .order_by('-count')[:10]
        )
        
        # 按操作类型统计
        operations_by_action = list(
            OperationLog.objects.filter(timestamp__gte=start_time)
            .values('action')
            .annotate(count=Count('id'))
            .order_by('-count')
        )
        
        # 按资源类型统计
        operations_by_resource = list(
            OperationLog.objects.filter(timestamp__gte=start_time)
            .values('resource_type')
            .annotate(count=Count('id'))
            .order_by('-count')
        )
        
        # 安全事件统计
        security_events_by_type = list(
            SecurityEvent.objects.filter(timestamp__gte=start_time)
            .values('event_type')
            .annotate(count=Count('id'))
            .order_by('-count')
        )
        
        security_events_by_severity = list(
            SecurityEvent.objects.filter(timestamp__gte=start_time)
            .values('severity')
            .annotate(count=Count('id'))
            .order_by('-count')
        )
        
        stats = {
            'total_operations': total_operations,
            'total_accesses': total_accesses,
            'total_security_events': total_security_events,
            'unresolved_security_events': unresolved_security_events,
            'operations_by_hour': operations_by_hour,
            'operations_by_day': operations_by_day,
            'top_active_users': top_active_users,
            'operations_by_action': operations_by_action,
            'operations_by_resource': operations_by_resource,
            'security_events_by_type': security_events_by_type,
            'security_events_by_severity': security_events_by_severity,
        }
        
        serializer = AuditStatsSerializer(stats)
        return Response(serializer.data)


class UserActivityView(APIView):
    """用户活动视图"""
    permission_classes = [IsAdminOrSuperUser]
    
    def get(self, request, user_id=None):
        """获取用户活动信息"""
        if user_id:
            # 获取特定用户的活动
            user = get_object_or_404(User, pk=user_id)
            users = [user]
        else:
            # 获取所有用户的活动概览
            users = User.objects.all()[:50]  # 限制数量
        
        activities = []
        for user in users:
            # 操作统计
            operations = OperationLog.objects.filter(user=user)
            total_operations = operations.count()
            last_operation = operations.order_by('-timestamp').first()
            
            # 访问统计
            accesses = AccessLog.objects.filter(user=user)
            total_accesses = accesses.count()
            last_access = accesses.order_by('-timestamp').first()
            
            # 安全事件统计
            security_events = SecurityEvent.objects.filter(user=user)
            security_events_count = security_events.count()
            last_security_event = security_events.order_by('-timestamp').first()
            
            activity = {
                'user_id': user.id,
                'user_name': user.get_full_name(),
                'user_email': user.email,
                'total_operations': total_operations,
                'last_operation_time': last_operation.timestamp if last_operation else None,
                'total_accesses': total_accesses,
                'last_access_time': last_access.timestamp if last_access else None,
                'security_events_count': security_events_count,
                'last_security_event_time': last_security_event.timestamp if last_security_event else None,
            }
            
            # 如果是查看特定用户，添加详细信息
            if user_id:
                activity['recent_operations'] = OperationLogSerializer(
                    operations.order_by('-timestamp')[:10],
                    many=True
                ).data
                activity['recent_accesses'] = AccessLogSerializer(
                    accesses.order_by('-timestamp')[:10],
                    many=True
                ).data
            
            activities.append(activity)
        
        if user_id:
            return Response(activities[0] if activities else {})
        else:
            return Response(activities)


class AuditExportView(APIView):
    """审计导出视图"""
    permission_classes = [IsAdminOrSuperUser]
    
    def post(self, request):
        """导出审计日志"""
        serializer = AuditExportSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        
        data = serializer.validated_data
        log_type = data['log_type']
        format_type = data['format']
        start_time = data.get('start_time')
        end_time = data.get('end_time')
        user_ids = data.get('user_ids')
        include_details = data.get('include_details', True)
        
        # 获取数据
        if log_type == 'operation':
            queryset = OperationLog.objects.select_related('user')
            model_name = 'operation_logs'
        elif log_type == 'access':
            queryset = AccessLog.objects.select_related('user')
            model_name = 'access_logs'
        else:  # security
            queryset = SecurityEvent.objects.select_related('user', 'resolved_by')
            model_name = 'security_events'
        
        # 应用过滤器
        if start_time:
            queryset = queryset.filter(timestamp__gte=start_time)
        if end_time:
            queryset = queryset.filter(timestamp__lte=end_time)
        if user_ids:
            queryset = queryset.filter(user_id__in=user_ids)
        
        # 限制导出数量
        queryset = queryset.order_by('-timestamp')[:10000]
        
        # 生成文件
        if format_type == 'csv':
            return self.export_csv(queryset, model_name, include_details)
        elif format_type == 'excel':
            return self.export_excel(queryset, model_name, include_details)
        else:  # json
            return self.export_json(queryset, model_name, include_details)
    
    def export_csv(self, queryset, model_name, include_details):
        """导出CSV格式"""
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = f'attachment; filename="{model_name}_{timezone.now().strftime("%Y%m%d_%H%M%S")}.csv"'
        
        writer = csv.writer(response)
        
        # 写入表头
        if model_name == 'operation_logs':
            headers = ['ID', '用户', '操作', '资源类型', '资源ID', 'IP地址', '时间', '成功']
            if include_details:
                headers.append('详情')
            writer.writerow(headers)
            
            # 写入数据
            for log in queryset:
                row = [
                    log.id,
                    log.user.email if log.user else '',
                    log.get_action_display(),
                    log.get_resource_type_display(),
                    log.resource_id,
                    log.ip_address,
                    log.timestamp.strftime('%Y-%m-%d %H:%M:%S'),
                    '是' if log.success else '否'
                ]
                if include_details:
                    row.append(json.dumps(log.details, ensure_ascii=False) if log.details else '')
                writer.writerow(row)
        
        # 类似处理其他日志类型...
        
        return response
    
    def export_excel(self, queryset, model_name, include_details):
        """导出Excel格式"""
        # 创建工作簿
        wb = Workbook()
        ws = wb.active
        ws.title = model_name
        
        # 写入表头和数据（类似CSV）
        # ...
        
        # 保存到内存
        output = io.BytesIO()
        wb.save(output)
        output.seek(0)
        
        response = HttpResponse(
            output.getvalue(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = f'attachment; filename="{model_name}_{timezone.now().strftime("%Y%m%d_%H%M%S")}.xlsx"'
        
        return response
    
    def export_json(self, queryset, model_name, include_details):
        """导出JSON格式"""
        # 序列化数据
        if model_name == 'operation_logs':
            serializer = OperationLogSerializer(queryset, many=True)
        elif model_name == 'access_logs':
            serializer = AccessLogSerializer(queryset, many=True)
        else:
            serializer = SecurityEventSerializer(queryset, many=True)
        
        data = serializer.data
        
        # 如果不包含详情，移除details字段
        if not include_details:
            for item in data:
                item.pop('details', None)
        
        response = HttpResponse(
            json.dumps(data, ensure_ascii=False, indent=2),
            content_type='application/json'
        )
        response['Content-Disposition'] = f'attachment; filename="{model_name}_{timezone.now().strftime("%Y%m%d_%H%M%S")}.json"'
        
        return response