from django.urls import path, include
from . import views

# 密码条目相关URL
password_urlpatterns = [
    path(
        "passwords/",
        views.PasswordEntryListCreateView.as_view(),
        name="password_list_create",
    ),
    path(
        "passwords/<uuid:pk>/",
        views.PasswordEntryDetailView.as_view(),
        name="password_detail",
    ),
    path(
        "passwords/<uuid:pk>/copy/",
        views.PasswordCopyView.as_view(),
        name="password_copy",
    ),
    path(
        "passwords/<uuid:pk>/toggle-favorite/",
        views.PasswordToggleFavoriteView.as_view(),
        name="password_toggle_favorite",
    ),
    path(
        "passwords/batch-delete/",
        views.batch_delete_passwords,
        name="password_batch_delete",
    ),
    # 分离的密码更新功能
    path(
        "passwords/<uuid:pk>/update-password/",
        views.PasswordUpdateOnlyView.as_view(),
        name="password_update_only",
    ),
    path(
        "passwords/<uuid:pk>/update-info/",
        views.PasswordInfoUpdateView.as_view(),
        name="password_info_update",
    ),
    path(
        "passwords/<uuid:password_entry_id>/custom-fields/",
        views.CustomFieldListCreateView.as_view(),
        name="custom_field_list_create",
    ),
    path(
        "passwords/<uuid:password_entry_id>/attachments/",
        views.AttachmentListCreateView.as_view(),
        name="attachment_list_create",
    ),
]

# 分类相关URL
category_urlpatterns = [
    path(
        "categories/",
        views.CategoryListCreateView.as_view(),
        name="category_list_create",
    ),
    path(
        "categories/<int:pk>/",
        views.CategoryDetailView.as_view(),
        name="category_detail",
    ),
]


# 工具相关URL
tool_urlpatterns = [
    path(
        "password-generator/",
        views.PasswordGeneratorView.as_view(),
        name="password_generator",
    ),
    path(
        "security-analysis/",
        views.PasswordSecurityAnalysisView.as_view(),
        name="security_analysis",
    ),
]

# 其他资源URL
resource_urlpatterns = [
    path(
        "custom-fields/<int:pk>/",
        views.CustomFieldDetailView.as_view(),
        name="custom_field_detail",
    ),
    path(
        "attachments/<int:pk>/",
        views.AttachmentDetailView.as_view(),
        name="attachment_detail",
    ),
]

# 组管理相关URL
group_urlpatterns = [
    path(
        "groups/",
        views.PasswordEntryGroupListCreateView.as_view(),
        name="group_list_create",
    ),
    path(
        "groups/<int:pk>/",
        views.PasswordEntryGroupDetailView.as_view(),
        name="group_detail",
    ),
    path(
        "group-permissions/",
        views.GroupPermissionListCreateView.as_view(),
        name="group_permission_list_create",
    ),
    path(
        "group-permissions/<int:pk>/",
        views.GroupPermissionDetailView.as_view(),
        name="group_permission_detail",
    ),
    path(
        "group-memberships/",
        views.PasswordEntryGroupMembershipListCreateView.as_view(),
        name="group_membership_list_create",
    ),
    path(
        "group-memberships/<int:pk>/",
        views.PasswordEntryGroupMembershipDetailView.as_view(),
        name="group_membership_detail",
    ),
]

# 系统管理专用URL
system_urlpatterns = [
    path(
        "systems/",
        views.ITOpsPasswordListView.as_view(),
        name="system_list",
    ),
    path(
        "stats/",
        views.ITOpsSystemStatsView.as_view(),
        name="system_stats",
    ),
    path(
        "system-types/",
        views.get_system_types,
        name="system_types",
    ),
    path(
        "environments/",
        views.get_environments,
        name="environments",
    ),
    path(
        "database-types/",
        views.get_database_types,
        name="database_types",
    ),
    path(
        "protocols/",
        views.get_protocols,
        name="protocols",
    ),
]

# 密码策略相关URL
policy_urlpatterns = [
    path(
        "password-policies/",
        views.PasswordPolicyListView.as_view(),
        name="password_policy_list_create",
    ),
    path(
        "password-policies/<int:pk>/",
        views.PasswordPolicyDetailView.as_view(),
        name="password_policy_detail",
    ),
    path(
        "password-policies/generate/",
        views.generate_password_by_policy,
        name="password_generate_by_policy",
    ),
    path(
        "password-policies/validate/",
        views.validate_password_by_policy,
        name="password_validate_by_policy",
    ),
]

# 回收站相关URL
recycle_bin_urlpatterns = [
    path(
        "recycle-bin/",
        views.RecycleBinListView.as_view(),
        name="recycle_bin_list",
    ),
    path(
        "recycle-bin/<uuid:pk>/restore/",
        views.restore_password,
        name="password_restore",
    ),
    path(
        "recycle-bin/<uuid:pk>/permanent-delete/",
        views.permanent_delete_password,
        name="password_permanent_delete",
    ),
]

urlpatterns = [
    path("", include(password_urlpatterns)),
    path("", include(category_urlpatterns)),
    path("", include(group_urlpatterns)),
    path("", include(tool_urlpatterns)),
    path("", include(resource_urlpatterns)),
    path("", include(system_urlpatterns)),
    path("", include(policy_urlpatterns)),
    path("", include(recycle_bin_urlpatterns)),
]
